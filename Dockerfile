FROM nginx:alpine

RUN apk add --no-cache gettext

COPY dist /usr/share/nginx/html

# 内嵌nginx配置
RUN cat > /etc/nginx/nginx.conf << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    sendfile on;
    keepalive_timeout 65;
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location /config.js {
            alias /usr/share/nginx/html/config.js;
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }

        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}
EOF

# 内嵌启动脚本
RUN cat > /entrypoint.sh << 'EOF'
#!/bin/sh
echo "生成配置文件..."
cat > /usr/share/nginx/html/config.js << EOL
window.__APP_CONFIG__ = {
  app: {
    title: '${VITE_APP_TITLE:-测试医院管理系统}',
    version: '${VITE_APP_VERSION:-1.0.0}',
    env: '${VITE_APP_ENV:-production}'
  },
  api: {
    baseURL: '${VITE_API_BASE_URL:-/api}',
    backendURL: '${VITE_BACKEND_URL:-http://localhost:6596}',
    timeout: ${VITE_API_TIMEOUT:-12000}
  },
  thirdParty: {
    yingchunhua: {
      sdkUrl: '${VITE_YINGCHUNHUA_SDK_URL:-http://**************:8094/client_app_iframe/index.js}',
      appKey: '${VITE_YINGCHUNHUA_APP_KEY:-}',
      appSecretKey: '${VITE_YINGCHUNHUA_APP_SECRET_KEY:-}',
      linkType: '2',
      qualityTarget: '2'
    }
  }
};
EOL
exec "$@"
EOF

RUN chmod +x /entrypoint.sh

EXPOSE 80

ENV VITE_API_BASE_URL=/api \
    VITE_API_TIMEOUT=12000 \
    VITE_BACKEND_URL=http://localhost:6596 \
    VITE_APP_TITLE=测试医院管理系统 \
    VITE_APP_ENV=production \
    VITE_APP_VERSION=1.0.0 \
    VITE_YINGCHUNHUA_SDK_URL=http://**************:8094/client_app_iframe/index.js \
    VITE_YINGCHUNHUA_APP_KEY=ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09 \
    VITE_YINGCHUNHUA_APP_SECRET_KEY=YYCloud1644286723584

ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
