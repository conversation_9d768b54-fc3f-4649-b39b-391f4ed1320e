// 运行时配置文件模板
// 此文件将在容器启动时根据环境变量生成实际的配置文件

window.__APP_CONFIG__ = {
  // 应用基础配置
  app: {
    title: '${VITE_APP_TITLE}',
    version: '${VITE_APP_VERSION}',
    env: '${VITE_APP_ENV}'
  },

  // API配置
  api: {
    baseURL: '${VITE_API_BASE_URL}',
    backendURL: '${VITE_BACKEND_URL}',
    timeout: parseInt('${VITE_API_TIMEOUT}') || 12000
  },

  // 第三方系统配置
  thirdParty: {
    yingchunhua: {
      sdkUrl: '${VITE_YINGCHUNHUA_SDK_URL}',
      appKey: '${VITE_YINGCHUNHUA_APP_KEY}',
      appSecretKey: '${VITE_YINGCHUNHUA_APP_SECRET_KEY}',
      linkType: '2',
      qualityTarget: '2'
    }
  }
};
