#!/bin/sh

echo "生成配置文件..."
cat > /usr/share/nginx/html/config.js << EOL
window.__APP_CONFIG__ = {
  app: {
    title: "${VITE_APP_TITLE:-测试医院管理系统}",
    version: "${VITE_APP_VERSION:-1.0.0}",
    env: "${VITE_APP_ENV:-production}"
  },
  api: {
    baseURL: "${VITE_API_BASE_URL:-/api}",
    backendURL: "${VITE_BACKEND_URL:-http://localhost:6596}",
    timeout: ${VITE_API_TIMEOUT:-12000}
  },
  thirdParty: {
    yingchunhua: {
      sdkUrl: "${VITE_YINGCHUNHUA_SDK_URL:-http://**************:8094/client_app_iframe/index.js}",
      appKey: "${VITE_YINGCHUNHUA_APP_KEY:-}",
      appSecretKey: "${VITE_YINGCHUNHUA_APP_SECRET_KEY:-}",
      linkType: "2",
      qualityTarget: "2"
    }
  }
};
EOL

exec "$@"
