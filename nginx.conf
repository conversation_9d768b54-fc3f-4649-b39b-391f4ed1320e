user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    sendfile on;
    keepalive_timeout 65;
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location /config.js {
            alias /usr/share/nginx/html/config.js;
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }

        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}
