#!/bin/bash

# YYHIS Web Docker部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}${1}${NC}"
}

print_message "🚀 YYHIS Web Docker部署脚本" $BLUE
print_message "================================" $BLUE

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    print_message "❌ Docker未安装，请先安装Docker" $RED
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    print_message "❌ Docker Compose未安装，请先安装Docker Compose" $RED
    exit 1
fi

# 创建必要的目录
print_message "📁 创建必要的目录..." $YELLOW
mkdir -p logs
mkdir -p config

# 停止并删除现有容器
print_message "🛑 停止现有容器..." $YELLOW
docker-compose down || true

# 构建镜像
print_message "🔨 构建Docker镜像..." $YELLOW
docker-compose build

# 启动服务
print_message "🚀 启动服务..." $YELLOW
docker-compose up -d

# 等待服务启动
print_message "⏳ 等待服务启动..." $YELLOW
sleep 10

# 检查服务状态
print_message "🔍 检查服务状态..." $YELLOW
if docker-compose ps | grep -q "Up"; then
    print_message "✅ 服务启动成功！" $GREEN
    print_message "🌐 访问地址: http://localhost:8080" $GREEN
    print_message "🏥 健康检查: http://localhost:8080/health" $GREEN
else
    print_message "❌ 服务启动失败，请检查日志" $RED
    docker-compose logs
    exit 1
fi

# 显示日志
print_message "📋 显示最近的日志..." $YELLOW
docker-compose logs --tail=20

print_message "🎉 部署完成！" $GREEN
print_message "💡 使用 'docker-compose logs -f' 查看实时日志" $BLUE
print_message "💡 使用 'docker-compose down' 停止服务" $BLUE
