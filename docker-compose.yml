version: '3.8'

services:
  yyhis-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: yyhis-web
    ports:
      - "8080:80"
    environment:
      # 应用基础配置
      - VITE_APP_TITLE=测试医院管理系统
      - VITE_APP_ENV=production
      - VITE_APP_VERSION=1.0.0
      
      # API配置
      - VITE_API_BASE_URL=/api
      - VITE_API_TIMEOUT=12000
      - VITE_BACKEND_URL=http://8.140.53.70:9090
      
      # 第三方系统配置
      - VITE_YINGCHUNHUA_SDK_URL=http://183.242.68.188:32103/client_app_iframe/index.js
      - VITE_YINGCHUNHUA_APP_KEY=ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09
      - VITE_YINGCHUNHUA_APP_SECRET_KEY=YYCloud1644286723584
    volumes:
      # 可选：映射自定义配置文件
      # - ./config/custom-config.js:/usr/share/nginx/html/config.js:ro
      # 可选：映射nginx配置
      # - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      # 日志映射
      - ./logs:/var/log/nginx
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# 可选：添加网络配置
networks:
  default:
    name: yyhis-network
