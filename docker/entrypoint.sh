#!/bin/sh

# Docker容器启动脚本
# 根据环境变量生成运行时配置文件

echo "🚀 正在启动容器..."
echo "📋 环境变量配置："
echo "   VITE_APP_TITLE: $VITE_APP_TITLE"
echo "   VITE_APP_ENV: $VITE_APP_ENV"
echo "   VITE_API_BASE_URL: $VITE_API_BASE_URL"
echo "   VITE_BACKEND_URL: $VITE_BACKEND_URL"
echo "   VITE_API_TIMEOUT: $VITE_API_TIMEOUT"

# 生成配置文件
echo "🔧 正在生成运行时配置文件..."
envsubst < /app/config/config.template.js > /usr/share/nginx/html/config.js

echo "✅ 配置文件生成完成"
echo "📄 配置文件内容："
cat /usr/share/nginx/html/config.js

# 启动nginx
echo "🌐 正在启动Nginx..."
exec "$@"
