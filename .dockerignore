# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/

# 开发工具
.vscode/
.idea/
*.swp
*.swo

# 日志文件
logs/
*.log

# 环境变量文件（保留一个用于构建）
.env.local
.env.development.local
.env.test.local
.env.production.local

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# 文档
*.md
docs/

# 测试
coverage/
.nyc_output/

# 临时文件
.tmp/
.cache/

# 操作系统
.DS_Store
Thumbs.db

# 其他
*.tgz
*.tar.gz
