# 多阶段构建 Dockerfile
# 第一阶段：构建阶段
FROM node:20.19.0-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用（使用生产环境配置）
RUN npm run build:prod

# 第二阶段：运行阶段
FROM nginx:alpine

# 安装必要工具
RUN apk add --no-cache gettext

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY docker/nginx.conf /etc/nginx/nginx.conf

# 创建配置目录
RUN mkdir -p /app/config

# 复制配置模板和启动脚本
COPY docker/config.template.js /app/config/
COPY docker/entrypoint.sh /app/

# 设置执行权限
RUN chmod +x /app/entrypoint.sh

# 暴露端口
EXPOSE 80

# 设置环境变量默认值
ENV VITE_API_BASE_URL=/api
ENV VITE_API_TIMEOUT=12000
ENV VITE_BACKEND_URL=http://localhost:6596
ENV VITE_APP_TITLE=测试医院管理系统
ENV VITE_APP_ENV=production
ENV VITE_APP_VERSION=1.0.0
ENV VITE_YINGCHUNHUA_SDK_URL=http://**************:8094/client_app_iframe/index.js
ENV VITE_YINGCHUNHUA_APP_KEY=ajNLOHF3dERoMmhob1FNSi9ZTFpJUT09
ENV VITE_YINGCHUNHUA_APP_SECRET_KEY=YYCloud1644286723584

# 使用自定义启动脚本
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
