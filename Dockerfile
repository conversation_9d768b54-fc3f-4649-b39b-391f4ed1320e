FROM nginx:alpine

COPY dist /usr/share/nginx/html

# 创建启动脚本
RUN echo '#!/bin/sh' > /entrypoint.sh && \
    echo 'echo "生成配置文件..."' >> /entrypoint.sh && \
    echo 'cat > /usr/share/nginx/html/config.js << EOL' >> /entrypoint.sh && \
    echo 'window.__APP_CONFIG__ = {' >> /entrypoint.sh && \
    echo '  app: {' >> /entrypoint.sh && \
    echo '    title: "${VITE_APP_TITLE:-测试医院管理系统}",' >> /entrypoint.sh && \
    echo '    version: "${VITE_APP_VERSION:-1.0.0}",' >> /entrypoint.sh && \
    echo '    env: "${VITE_APP_ENV:-production}"' >> /entrypoint.sh && \
    echo '  },' >> /entrypoint.sh && \
    echo '  api: {' >> /entrypoint.sh && \
    echo '    baseURL: "${VITE_API_BASE_URL:-/api}",' >> /entrypoint.sh && \
    echo '    backendURL: "${VITE_BACKEND_URL:-http://localhost:6596}",' >> /entrypoint.sh && \
    echo '    timeout: ${VITE_API_TIMEOUT:-12000}' >> /entrypoint.sh && \
    echo '  },' >> /entrypoint.sh && \
    echo '  thirdParty: {' >> /entrypoint.sh && \
    echo '    yingchunhua: {' >> /entrypoint.sh && \
    echo '      sdkUrl: "${VITE_YINGCHUNHUA_SDK_URL:-http://**************:8094/client_app_iframe/index.js}",' >> /entrypoint.sh && \
    echo '      appKey: "${VITE_YINGCHUNHUA_APP_KEY:-}",' >> /entrypoint.sh && \
    echo '      appSecretKey: "${VITE_YINGCHUNHUA_APP_SECRET_KEY:-}",' >> /entrypoint.sh && \
    echo '      linkType: "2",' >> /entrypoint.sh && \
    echo '      qualityTarget: "2"' >> /entrypoint.sh && \
    echo '    }' >> /entrypoint.sh && \
    echo '  }' >> /entrypoint.sh && \
    echo '};' >> /entrypoint.sh && \
    echo 'EOL' >> /entrypoint.sh && \
    echo 'exec "$@"' >> /entrypoint.sh && \
    chmod +x /entrypoint.sh

EXPOSE 80

ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
