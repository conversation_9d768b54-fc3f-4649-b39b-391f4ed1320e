# YYHIS Web Docker部署文档

## 概述

本文档介绍如何使用Docker部署YYHIS Web前端应用，支持运行时配置管理，无需重新构建镜像即可修改配置。

## 特性

- ✅ **运行时配置**：支持通过环境变量或配置文件映射动态修改配置
- ✅ **多阶段构建**：优化镜像大小，提高构建效率
- ✅ **Nginx优化**：包含Gzip压缩、缓存策略、安全头等优化
- ✅ **健康检查**：内置健康检查机制
- ✅ **日志管理**：支持日志映射和查看
- ✅ **生产就绪**：适用于生产环境部署

## 快速开始

### 1. 使用Docker Compose部署（推荐）

```bash
# 克隆项目
git clone <your-repo>
cd yyhis-web

# 使用部署脚本
chmod +x docker/deploy.sh
./docker/deploy.sh
```

### 2. 手动部署

```bash
# 构建镜像
docker build -t yyhis-web .

# 运行容器
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -e VITE_BACKEND_URL=http://your-backend-server:9090 \
  -e VITE_APP_TITLE=你的医院管理系统 \
  yyhis-web
```

## 配置管理

### 环境变量配置

支持以下环境变量：

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| `VITE_APP_TITLE` | 测试医院管理系统 | 应用标题 |
| `VITE_APP_ENV` | production | 应用环境 |
| `VITE_APP_VERSION` | 1.0.0 | 应用版本 |
| `VITE_API_BASE_URL` | /api | API基础路径 |
| `VITE_API_TIMEOUT` | 12000 | API超时时间(ms) |
| `VITE_BACKEND_URL` | http://localhost:6596 | 后端服务器地址 |
| `VITE_YINGCHUNHUA_SDK_URL` | ... | 迎春花SDK地址 |
| `VITE_YINGCHUNHUA_APP_KEY` | ... | 迎春花应用Key |
| `VITE_YINGCHUNHUA_APP_SECRET_KEY` | ... | 迎春花应用密钥 |

### Docker Compose配置示例

```yaml
version: '3.8'
services:
  yyhis-web:
    image: yyhis-web
    ports:
      - "8080:80"
    environment:
      - VITE_BACKEND_URL=http://*************:9090
      - VITE_APP_TITLE=生产环境医院管理系统
      - VITE_API_TIMEOUT=15000
    volumes:
      - ./logs:/var/log/nginx
```

### 配置文件映射（高级）

如果需要更复杂的配置，可以直接映射配置文件：

```bash
# 创建自定义配置文件
cat > custom-config.js << 'EOF'
window.__APP_CONFIG__ = {
  app: {
    title: '自定义医院管理系统',
    version: '2.0.0',
    env: 'production'
  },
  api: {
    baseURL: '/api',
    backendURL: 'https://api.yourhospital.com',
    timeout: 20000
  },
  thirdParty: {
    yingchunhua: {
      sdkUrl: 'https://your-custom-sdk.com/index.js',
      appKey: 'your-app-key',
      appSecretKey: 'your-secret-key'
    }
  }
};
EOF

# 映射配置文件
docker run -d \
  --name yyhis-web \
  -p 8080:80 \
  -v $(pwd)/custom-config.js:/usr/share/nginx/html/config.js:ro \
  yyhis-web
```

## 部署命令

### 基本命令

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看服务状态
docker-compose ps
```

### 生产环境部署

```bash
# 生产环境配置
export VITE_BACKEND_URL=https://api.yourhospital.com
export VITE_APP_TITLE=生产环境医院管理系统
export VITE_API_TIMEOUT=15000

# 启动服务
docker-compose up -d
```

## 监控和维护

### 健康检查

访问 `http://localhost:8080/health` 检查服务状态

### 日志查看

```bash
# 查看应用日志
docker-compose logs yyhis-web

# 查看Nginx访问日志
docker exec yyhis-web tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
docker exec yyhis-web tail -f /var/log/nginx/error.log
```

### 性能优化

1. **启用Gzip压缩**：已在Nginx配置中启用
2. **静态资源缓存**：已配置1年缓存策略
3. **安全头**：已添加必要的安全响应头

## 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs yyhis-web
   
   # 检查配置文件
   docker exec yyhis-web cat /usr/share/nginx/html/config.js
   ```

2. **API请求失败**
   - 检查 `VITE_BACKEND_URL` 配置是否正确
   - 确认后端服务是否可访问
   - 检查网络连接

3. **配置不生效**
   - 重启容器：`docker-compose restart`
   - 检查环境变量：`docker-compose exec yyhis-web env`

### 调试模式

```bash
# 进入容器调试
docker-compose exec yyhis-web sh

# 查看生成的配置文件
docker-compose exec yyhis-web cat /usr/share/nginx/html/config.js

# 测试Nginx配置
docker-compose exec yyhis-web nginx -t
```

## 安全建议

1. **使用HTTPS**：生产环境建议配置SSL证书
2. **限制访问**：配置防火墙规则限制访问
3. **定期更新**：定期更新基础镜像和依赖
4. **敏感信息**：使用Docker secrets管理敏感配置

## 更新部署

```bash
# 拉取最新代码
git pull

# 重新构建并部署
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```
